<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clothing Store</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-blue-600">Free-Styles</a>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-blue-600 transition">Home</a>
                    <a href="products.html" class="text-blue-600 font-semibold">Products</a>
                    <a href="about.html" class="text-gray-700 hover:text-blue-600 transition">About</a>
                    <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition">Contact</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="cart.html" class="relative text-gray-700 hover:text-blue-600 transition">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <span id="cart-count" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">0</span>
                    </a>
                    <button id="mobile-menu-btn" class="md:hidden text-gray-700">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <a href="index.html" class="block py-2 text-gray-700 hover:text-blue-600">Home</a>
                <a href="products.html" class="block py-2 text-blue-600 font-semibold">Products</a>
                <a href="about.html" class="block py-2 text-gray-700 hover:text-blue-600">About</a>
                <a href="contact.html" class="block py-2 text-gray-700 hover:text-blue-600">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="bg-blue-600 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <h1 class="text-4xl font-bold">Our Products</h1>
            <p class="text-xl mt-2">Discover our amazing collection of Clothing</p>
        </div>
    </section>

    <!-- Filters -->
    <section class="py-8 bg-white">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex flex-wrap gap-4 items-center">
                <label class="font-semibold">Filter by Category:</label>
                <select id="category-filter" class="border border-gray-300 rounded-lg px-4 py-2">
                    <option value="all">All Categories</option>
                    <option value="smartphone">Men's Clothes</option>
                    <option value="laptop">Women's Clothes</option>
                    <option value="accessory">Accessories</option>
                    <option value="gaming">Children's Clothes</option>
                </select>
                <label class="font-semibold ml-4">Sort by:</label>
                <select id="sort-filter" class="border border-gray-300 rounded-lg px-4 py-2">
                    <option value="name">Name</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                </select>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                <?php 
                    include '../connection.php';
                    global $con;
                    $select="SELECT * FROM `tbproducts`";
                    $exe=$con->query($select);
                    while ($row=$exe->fetch_assoc()){
                        echo '
                            <div class="bg-white p-8 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img src="../upload/'.$row['image'].'" alt="${product.name}" class="w-full h-48 object-cover">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-2 flex justify-center text-center text-dark-1000">'.$row['name'].'</h3>
                <p class="text-red-600 mb-4 text-lg">Price: '.$row['price'].'$</p>
                <p class="text-blue-600 mb-4">Description: '.$row['description'].'</p>
            
                <div class="flex items-center justify-between">
                    <span class="text-2xl font-bold text-blue-600"></span>
                    <div class="flex space-x-2">
                        <button  class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-2 rounded transition">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition "
                               > 
                          <i class="fas fa-cart-plus"></i>
                        </button>
                    </div>
                </div>
                
            </div>
        </div>
                        ';
                    }
                ?>
            
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">TechStore</h3>
                    <p class="text-gray-400">Your trusted partner for all technology needs.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white">Home</a></li>
                        <li><a href="products.html" class="text-gray-400 hover:text-white">Products</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-white">About</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Categories</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Smartphones</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Laptops</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Accessories</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Gaming</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Contact Info</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><i class="fas fa-phone mr-2"></i> +****************</li>
                        <li><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
                        <li><i class="fas fa-map-marker-alt mr-2"></i> 123 Tech Street, City</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 TechStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>