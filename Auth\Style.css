*{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: sans-serif;
}
.container{
    width: 100%;
    height: 100vh;
}
form{
    width: 400px;
    margin: 100px auto;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    padding: 30px;
    border-radius: 10px;
}

h3{
    text-align: center;
}

.form-group{
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 10px;
}
input {
    padding:10px;
    border: none;
    border-radius: 5px;
    outline: none;
    border: 1px solid #333;
}

.form-button{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    gap: 20px;
}
button{
    width: 100%;
    padding: 10px;
    background-color: rgba(44, 44, 255);
    color: #fff;
    font-size: 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}
button:hover{
    background-color: rgb(33, 33, 193);
    ;
}