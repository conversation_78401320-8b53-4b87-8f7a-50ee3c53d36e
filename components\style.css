*{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}
.contain{
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

}
.aside{
    width: 30%;
    height: 100%;
    background-color: #ebebeb;
    display: flex;
    flex-direction: column;
    gap: 30px;
    justify-content: start;
    align-items: center;
    padding: 50px 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    h1{
        font-size: 50px;
        font-weight: bold;
    }
    nav {
        width: 100%;
        ul{
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 20px;
            list-style: none;
            padding: 0;
            li{
                width: 100%;
                padding: 10px 0;
                a{
                    text-decoration: none;
                    color: #000;
                    font-size: 18px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    color: #000;
                } 
            }
            li:hover{
                background-color: #fff;
            }
        }
    }
}
main{
    width: 85%;
    min-height: 100vh;
    .header{
        width: 100%;
        height: 100px;
        display: flex;
        justify-content: space-between;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        .search{
            width: 60%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            input{
                width: 60%;
                border: none;
                outline: none;
                border-radius: 5px;
                padding: 0 10px;
                font-size: 16px;
            }
            i{
                position: absolute;
                top: 50%;
                left: 21%;
                transform: translateY(-50%);
                cursor: pointer;
            }

        }
        .user{
            display: flex;
            height: 100%;
            align-items: center;
            gap: 10px;
            padding: 0 20px;
            p{
                margin-top: 20px;
            }
        }
    }
}
.content{
    margin-top: 10px;
    width: 100%;
}
form{
    width: 600px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}