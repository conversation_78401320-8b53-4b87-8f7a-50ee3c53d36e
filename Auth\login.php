<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="./Style.css">
    <title>Login</title>
</head>
<body>
    <div class="container">
        <form action="" method="post">
            <h3>Login Form</h3>
            <div class="form-group">
                <label for="">Email</label>
                <input type="text" name="email" id="" class="form-control" placeholder="Enter your email">
            </div>
            <div class="form-group">
                <label for="">Password</label>
                <input type="password" name="password" id="" class="form-control" placeholder="Enter your password">
            </div>
            <div class="form-button">
                <a href="Register.php">Create Account</a>
                <button name="login">Login</button>
            </div>
        </form>
    </div>
</body>
</html>

<?php
session_start();
include '../connection.php';
if(isset($_POST['login'])){
    $email = $_POST['email'];
    $password = $_POST['password'];

    global $con;
    $getuser = "SELECT * FROM `tbuser` WHERE `email` = '$email' AND `password` = '$password' ";
    $exe = $con->query($getuser);
    if($exe->num_rows>0) {
        $_SESSION['login']=$email;
        $user = $exe->fetch_assoc();
        $_SESSION['role'] = $user['role'];
        if($user['role']==0){
            header('location: ../Frontend/index.php');
        }else{
            header('location: ../Backend/index.php');
        }
    
        
    }else{
        header('location: login.php');

    }

}
?>

